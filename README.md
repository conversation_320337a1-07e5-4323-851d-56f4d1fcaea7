# Firebase Product Management System

This is a [Next.js](https://nextjs.org) project with Firebase integration for product management.

## Features

- **Authentication**: User login/logout with Firebase Auth
- **Product Management**: CRUD operations for products
- **Product Types**: Dynamic product type management
- **Product Lists**: Display products with pagination
- **Auto-logout**: 15-minute inactivity timeout
- **Real-time Data**: Firebase Firestore integration

## Getting Started

### Prerequisites

1. Node.js (v18 or higher)
2. Firebase CLI (`npm install -g firebase-tools`)
3. Firebase project with Firestore enabled

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Configure Firebase:
   - Update `src/app/firebase/config.ts` with your Firebase configuration
   - Deploy Firestore security rules:

```bash
firebase login
firebase use your-project-id
firebase deploy --only firestore:rules
```

### Development

Run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Firebase Configuration

### Firestore Security Rules

The project includes security rules that:
- Allow authenticated users to read/write products and product types
- Allow users to manage their own user data
- Deny all other access

### Collections

- `products`: Product data with fields like name, price, type, etc.
- `productTypes`: Available product categories
- `users`: User profile data

## Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
