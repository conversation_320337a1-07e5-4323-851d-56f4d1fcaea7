rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read and write products
    match /products/{productId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow authenticated users to read and write product types
    match /productTypes/{typeId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow authenticated users to read and write their own user data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
